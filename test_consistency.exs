#!/usr/bin/env elixir

# Test script to verify that Operations and Ecto extension work consistently
# without needing operation_module in context

Mix.install([
  {:drops, path: "."}
])

defmodule TestApp do
  use Drops.Operations, repo: nil
end

defmodule TestOperation do
  use TestApp, type: :command

  schema do
    required :name, :string
    optional :age, :integer
  end

  @impl true
  def execute(context) do
    params = Map.get(context, :params)
    {:ok, %{message: "Hello #{params.name}!"}}
  end
end

defmodule TestEctoApp do
  use Drops.Operations, repo: nil
end

# Define a simple Ecto schema for testing
defmodule TestUser do
  use Ecto.Schema

  schema "users" do
    field :name, :string
    field :email, :string
  end
end

defmodule TestEctoOperation do
  use TestEctoApp, type: :command

  schema(TestUser)

  @impl true
  def execute(context) do
    changeset = Map.get(context, :changeset)
    {:ok, %{name: changeset.changes.name}}
  end

  def validate_changeset(%{changeset: changeset}) do
    import Ecto.Changeset
    changeset
    |> validate_required([:name])
    |> validate_length(:name, min: 2)
  end
end

# Test basic operation
IO.puts("Testing basic operation...")
result = TestOperation.call(%{params: %{name: "World", age: 25}})
IO.inspect(result, label: "Basic operation result")

# Test Ecto operation
IO.puts("\nTesting Ecto operation...")
result = TestEctoOperation.call(%{params: %{name: "John", email: "<EMAIL>"}})
IO.inspect(result, label: "Ecto operation result")

# Test Ecto operation with validation error
IO.puts("\nTesting Ecto operation with validation error...")
result = TestEctoOperation.call(%{params: %{name: "J", email: "<EMAIL>"}})
IO.inspect(result, label: "Ecto operation validation error")

IO.puts("\nAll tests completed successfully! ✅")
